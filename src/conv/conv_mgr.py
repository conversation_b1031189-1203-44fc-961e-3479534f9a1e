import asyncio
import json
import logging
import uuid
from multiprocessing import Event as ProcessEvent
from threading import Event as ThreadEvent
from typing import Any

from agents.service_factory.transcriber_factory import create_stt
from agents.service_factory.voice_factory import create_tts
from conv.conv_meta import VoiceConfig
from log.langfuse import get_langfuse
import sentry_sdk
from livekit import rtc
from livekit.agents import JobContext, AgentSession, ConversationItemAddedEvent
from livekit.agents import UserStateChangedEvent
from livekit.plugins import openai, silero
from livekit.rtc import RemoteTrackPublication, RemoteParticipant

from agents.agent_config import get_agent_info_from_meta, AgentConfig
from agents.lumi_agent import LumiAgent
from agents.agent_instructions import AgentInstructions
from app.config import get_config
from conv.analysers.analyser import AnalyserConfig
from conv.analysers.analysers import create_conv_analyser
from conv.room_config import RoomConfig
from conv.conv_svc import ConvSvc
from conv.features.ask_turn_detector import AskTurnDetector
from conv.features.participant_awaiter import ParticipantAwaiter
from conv.features.sentiments_handler import <PERSON>timent<PERSON><PERSON>yzer
from conv.features.silence_handler import SilenceHandler
from conv.features.turndetector.vad_profiles import get_vad_profile
from log import conv_log_svc
from log.conv_log_svc import ConversationLogger
from log.sentry import sentry_span
from conv.usage_collector import UsageCollector

def create_event():
    try:
        return ProcessEvent()
    except ImportError:
        return ThreadEvent()

_config = get_config()
_logger = logging.getLogger(__name__)
_logger.propagate = False

@sentry_span(op="room.build_ses_opts", description="build session options")
def build_conv_meta(room: rtc.Room, metadata: str = ''):
    _metadata = metadata if metadata else (room.metadata if len(room.metadata) > 0 else "")
    _logger.debug(f"Using metadata: {_metadata}")

    room_meta = RoomConfig()
    if _metadata:
        try:
            room_meta = RoomConfig().load_json(_metadata)
        except json.JSONDecodeError as e:
            _logger.warning(f"Failed to parse metadata as JSON: {e}")
        except Exception as e:
            _logger.warning(f"Error processing metadata: {e}")

    return room_meta

class ConvManager:
    _tasks = set[asyncio.Task[Any]]()
    _lock = asyncio.Lock()
    meta_updated = create_event()
    conversation_ended = create_event()
    conversation_finalizing = create_event()
    _conversation_logger: ConversationLogger = ConversationLogger()
    _api_client: conv_log_svc = None
    conv_meta: RoomConfig = None
    _raw_metadata: str = None  # Store the raw metadata string

    ctx: JobContext = None
    room: rtc.Room = None

    agent: LumiAgent = None
    silence_handler: SilenceHandler = None
    langfuse_client = get_langfuse()

    def update_metadata(self, metadata: str):
        self._raw_metadata = metadata  # Store the raw metadata string
        self.conv_meta = build_conv_meta(self.room, metadata)
        self._api_client = ConvSvc(conversation_type=self.conv_meta.type)
        self.meta_updated.set()

    def get_metadata(self) -> str:
        return self._raw_metadata

    @sentry_span(op="manager", description="wake up")
    def __init__(self, ctx: JobContext, session_id: str = str(uuid.uuid4())):
        self.sentiment_analyser = None
        self.agent_instructions = None
        self.agent_config : AgentConfig = None
        self.session: AgentSession = None
        self.conv_analyser = None
        self.last_activity = None
        self._is_welcome_played = False
        self.ctx = ctx
        self.room = ctx.room
        self.conversation_finalizing = create_event()
        self.conversation_ended = create_event()
        self._shutting_down = False

        # Initialize usage collection collector
        self.usage_collector = UsageCollector(ctx)

        # Initialize silence handler with callbacks
        self.silence_handler = SilenceHandler(
            say_idle_callback=self._handle_idle_message,
            end_conversation_callback=self.set_end_conversation,
            user_away_timeout=_config.app.silence_threshold
        )

        # Initialize participant awaiter
        self.participant_awaiter = ParticipantAwaiter(
            ctx=ctx,
            end_conversation_callback=self.set_end_conversation
        )

        _logger.debug("ConvManager initialized with usage collection collector, silence handler, and participant awaiter")

    @property
    def participant_is_joined(self):
        """Access the participant_is_joined event from the ParticipantAwaiter."""
        return self.participant_awaiter.participant_is_joined

    def _handle_idle_message(self):
        """Handle idle message callback from silence handler."""
        try:
           self.session.say_idle_message()
        except Exception as e:
            _logger.error(f"Error sending idle message: {e}")

    def register_conv_history_handler(self, conversation_id: str, conversation_type: str, company_id: str, log_conversation):
        """Register conversation history handler for logging and sentiment analysis."""
        @self.session.on("conversation_item_added")
        def on_conversation_item_added(event: ConversationItemAddedEvent):
            for content in event.item.content:
                if isinstance(content, str):
                    print(f" - text: {content}")
                    self.add_task(log_conversation(
                        actor=event.item.role,
                        message=content,
                        conversation_type=conversation_type,
                        company_id=company_id,
                        conversation_id=conversation_id,
                    ))
                    if event.item.role == "user":
                        self.add_task(self.sentiment_analyser.process_sentiment_analysis(conversation_id, content))

    @sentry_span(op="agent.setup", description="setup features")
    def register_conv_features(self):

        self.silence_handler.setup_session_events(self.session)

        self.sentiment_analyser = SentimentAnalyzer(config=get_config(), core_api_client=self._api_client)

        self.conv_analyser = create_conv_analyser(
            core_api_client=self._api_client,
            company_name=self.conv_meta.context.companyName,
            config=AnalyserConfig(prompt=self.conv_meta.analysis.prompt)
        )

        conversation_id = self.conv_meta.context.conversationId
        conversation_type = self.conv_meta.type
        company_id = self.conv_meta.context.companyId
        log_conversation = self._conversation_logger.log_conversation

        self.register_conv_history_handler(conversation_id, conversation_type, company_id, log_conversation)

    def _create_tts(self, voice_config: VoiceConfig):
        provider = voice_config.provider
        return create_tts(provider=provider, voice_info=voice_config)

    @sentry_span("agent.init", "init agent")
    async def init_agent(self):
        self.agent_config = get_agent_info_from_meta(self.conv_meta)
        self.agent_instructions = AgentInstructions(agent_info=self.agent_config,
                                                    profile_prompt=self.conv_meta.llm.profilePrompt,
                                                    languages=self.conv_meta.llm.language)

        # self.session = AgentSession(
        #        llm=openai.realtime.RealtimeModel(voice="echo"),
        # )
        vad_profile = get_vad_profile(self.agent_config.voice.vad_profile)
        vad = silero.VAD.load(**vad_profile.to_dict())
        self.session = AgentSession(
            stt=create_stt(language=self.conv_meta.llm.language),
            llm=openai.LLM(model=self.conv_meta.llm.model, temperature=_config.openai.temperature),
            tts=self._create_tts(self.agent_config.voice),
            vad=vad,
            min_interruption_words=2,
            min_interruption_duration=1,
            turn_detection=AskTurnDetector(vad=vad),
            user_away_timeout=self.silence_handler.user_away_timeout
        )
        self.register_conv_features()

        self.agent = LumiAgent(
            opts=self.agent_instructions,
            conv_id=self.conv_meta.context.conversationId,
            company_id=self.conv_meta.context.companyId,
            timezone=self.conv_meta.context.timezone,
            analyser=self.conv_analyser,
            api_client=self._api_client,
            room=self.room,
        )

        self.usage_collector.setup_metrics_collection(self.session)
        return await self.agent.initialize(self.session)

    def set_end_conversation(self):
        self.conversation_ended.set()
        self.conversation_finalizing.set()
        self.add_task(self.agent.finish_call(self.session))

    @sentry_span(op="room.shutdown", description="shutdown")
    async def shutdown_callback(self, obj=None):
        try:
            if self.conversation_ended.is_set():
                _logger.info("Conversation already ended. Skipping shutdown.")
                return

            _logger.info("Initiating conversation shutdown...")
            self.set_end_conversation()

            # Shutdown silence handler
            if self.silence_handler:
                self.silence_handler.shutdown()

            await self.ctx.room.disconnect()
            _logger.info("Room disconnected successfully")

        except Exception as e:
            _logger.error(f"Error shutting down conversation: {str(e)}")
            sentry_sdk.capture_exception(e)

        _logger.info(f"Shutdown callback execution completed: {obj}")

    def register_shutdown_callback(self):
        self.ctx.add_shutdown_callback(self.shutdown_callback)
        # Also register usage logging callback
        self.usage_collector.register_shutdown_logging()
        _logger.debug("Shutdown and usage logging callbacks registered with JobContext")

    def get_usage_summary(self):
        return self.usage_collector.get_current_summary()

    def add_task(self, task):
        asyncio.create_task(self._add_task_async(task))

    async def _add_task_async(self, task):
        if not asyncio.iscoroutine(task):
            raise ValueError("Task must be an awaitable coroutine")
        async_task = asyncio.create_task(task)
        async with self._lock:
            self._tasks.add(async_task)
        return async_task

    async def wait_for_participant(self):
        if self.conv_meta and self.conv_meta.context.conversationId:
            self.participant_awaiter.conversation_id = self.conv_meta.context.conversationId

        return await self.participant_awaiter.wait_for_participant()
