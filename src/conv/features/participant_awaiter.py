import asyncio
import logging
from typing import Callable, Optional, Any
from multiprocessing import Event as Process<PERSON><PERSON>
from threading import Event as Thread<PERSON>vent

from livekit.agents import JobContext
from livekit.rtc import RemoteTrackPublication, RemoteParticipant
from log.sentry import sentry_span

_logger = logging.getLogger(__name__)


def create_event():
    """Create an event that works in both multiprocessing and threading contexts."""
    try:
        return ProcessEvent()
    except ImportError:
        return ThreadEvent()


class ParticipantAwaiter:
    """
    Handles waiting for participants to join the room and be fully available.
    This class manages participant track monitoring and call status changes.
    """

    def __init__(self,
                 ctx: JobContext,
                 end_conversation_callback: Callable[[], None],
                 conversation_id: Optional[str] = None):
        """
        Initialize the ParticipantAwaiter.

        Args:
            ctx: JobContext containing room and participant information
            end_conversation_callback: Callback to call when conversation should end
            conversation_id: Optional conversation ID for logging
        """
        self.ctx = ctx
        self.end_conversation_callback = end_conversation_callback
        self.conversation_id = conversation_id or "unknown"

        # Create the participant_is_joined event internally
        self.participant_is_joined = create_event()

    @property
    def participant_is_joined_event(self):
        """Provide access to the participant_is_joined event for backward compatibility."""
        return self.participant_is_joined
        
    @sentry_span(op="room", description="wait callee")
    async def wait_for_participant(self):
        """
        Wait for a participant to join the room and be fully available.
        This method handles waiting for participant tracks and monitoring call status changes.
        
        Returns:
            The participant object when fully available
            
        Raises:
            Exception: If participant is not available after timeout or call is hung up
        """
        # Create a future that will be resolved when participant is fully available
        future = asyncio.get_event_loop().create_future()

        participant_obj = await self.ctx.wait_for_participant()
        
        # Check if participant already has tracks
        if len(participant_obj.track_publications) > 0:
            self.participant_is_joined.set()
            if not future.done():
                future.set_result(participant_obj)
        else:
            # Event handler for track publications
            @self.ctx.room.on("track_published")
            @sentry_span(op="room.track_published", description="callee added track")
            def track_published(publication: RemoteTrackPublication, participant: RemoteParticipant):
                _logger.info(f"{participant.identity} published track: {publication.name}")
                self.participant_is_joined.set()
                if not future.done():
                    future.set_result(participant_obj)

        @sentry_span(op="participant.attributes_changed", description="callee changed state")
        @self.ctx.room.on("participant_attributes_changed")
        def participant_attributes_changed(changed_attributes, participant):
            _logger.info(f"{participant.identity} attributes changed {changed_attributes}")
            call_status_changed_to = changed_attributes.get('sip.callStatus', '')
            
            if call_status_changed_to == 'active':
                self.participant_is_joined.set()
                _logger.info("callStatus: active")
                # Check if we can resolve the future
                if participant_obj and len(participant_obj.track_publications) > 0 and not future.done():
                    future.set_result(participant_obj)
            elif call_status_changed_to == 'hangup':
                self.end_conversation_callback()
                _logger.info("callStatus: hangup. ending conversation")
                if not future.done():
                    future.set_exception(Exception("Call hungup before participant was fully available"))
            else:
                _logger.info(f"callStatus: {call_status_changed_to} (no action taken)")

        try:
            # Wait for the future to be resolved with a timeout
            await asyncio.wait_for(future, timeout=160.0)  # 160 second timeout
            participant = future.result()
            _logger.info(
                f"Participant fully available: {participant} conv:{self.conversation_id}")
            return participant
        except asyncio.TimeoutError:
            _logger.warning("Timed out waiting for participant to be fully available")
            # Return the participant anyway, even if not fully ready
            if participant_obj:
                _logger.info(f"Returning participant despite timeout: {participant_obj}")
                return participant_obj
            raise Exception("No participant available after timeout")
