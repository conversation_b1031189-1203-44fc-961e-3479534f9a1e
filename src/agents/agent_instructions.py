import random
import re
from datetime import datetime
from zoneinfo import ZoneInfo
from typing import List, Tuple

from agents.agent_config import AgentConfig
from agents.language_config import LanguageSettings
from config import load_config


class AgentInstructions:
    languages = {}

    def __init__(self, agent_info: AgentConfig, profile_prompt=None, languages=None):
        # Load configuration from YAML files
        self._prompts_config = load_config('prompts')
        self._messages_config = load_config('messages')
        self._lang_config = load_config('language_mappings')

        self.config = agent_info
        self.profile_prompt = profile_prompt
        self.lang_settings = LanguageSettings(languages)
        self.tools_prompt = self._build_tools_prompt()
        self.default_prompt = """"""

        # Language settings for instructions
        self.languages = self._parse_language(languages)
        self.default_language = self.languages[0] if self.languages else self._lang_config.get('default_language', 'en-US')

    @property
    def response_style(self) -> str:
        """Get response style from YAML configuration."""
        return self._prompts_config.get('response_style', '')

    @property
    def silence_phrases(self) -> dict:
        """Get silence phrases from YAML configuration."""
        return self._messages_config.get('silence_phrases', {})

    def _build_tools_prompt(self) -> str:
        """Build tools prompt from YAML configuration."""
        base_prompt = self._prompts_config.get('tools_prompt_base', '')
        function_prompts = self._prompts_config.get('function_prompts', {})

        prompt_parts = [base_prompt]
        for function in self.config.functions:
            if function in function_prompts:
                prompt_parts.append(function_prompts[function])

        return "\n".join(prompt_parts)

    def get_time_update(self, timezone):
        """Get formatted time update string."""
        now = datetime.now(ZoneInfo(timezone))
        weekday = now.weekday()
        time_format = self._messages_config.get('time_format',
                                                "Current time: {current_time}  Weekday: {weekday} Time zone: {timezone}")
        return time_format.format(current_time=now, weekday=weekday, timezone=timezone)

    def get_silence_message(self, language=None) -> str:
        """Get a random silence message for the specified language."""
        locale = self.lang_settings.get_locale(language)
        phrases = self.silence_phrases.get(locale, self.silence_phrases.get('en-US', []))
        return random.choice(phrases) if phrases else "Are you still there?"

    def _parse_language(self, language_str: str) -> List[str]:
        """Parse language string into list of languages."""
        if not language_str:
            return [self._lang_config.get('default_language', 'en-US')]

        separator_pattern = self._lang_config.get('language_parsing.separators', '[;,]')
        languages = [lang.strip() for lang in re.split(separator_pattern, language_str) if lang.strip()]
        return languages

    @property
    def language_codes_to_names(self) -> dict:
        """Get language codes to names mapping from configuration."""
        return self._lang_config.get('language_codes_to_names', {})

    @property
    def language_codes_to_locale(self) -> dict:
        """Get language codes to locale mapping from configuration."""
        return self._lang_config.get('language_codes_to_locale', {})

    def map_language_code(self, lang_code: str) -> Tuple[str, str]:
        """Map language code to human-readable name and locale."""
        lang_code = lang_code.lower()
        language_name = self.language_codes_to_names.get(lang_code, 'English')
        locale = self.language_codes_to_locale.get(lang_code, 'en-US')
        return language_name, locale

    def get_language_instructions(self) -> str:
        """Get language instructions based on configured languages."""
        if len(self.languages) == 1:
            lang_name, _ = self.map_language_code(self.languages[0])
            if lang_name == "Arabic":
                template = self._messages_config.get('language_instructions.single_language_arabic',
                                                   "**Language Instructions:**\n- You should respond **only** in {language_name}. Use UAE accent.")
            else:
                template = self._messages_config.get('language_instructions.single_language',
                                                   "**Language Instructions:**\n- You should respond **only** in {language_name}.")
            return template.format(language_name=lang_name)

        supported_langs = [self.map_language_code(lang)[0] for lang in self.languages]
        default_lang, _ = self.map_language_code(self.languages[0])
        template = self._messages_config.get('language_instructions.multiple_languages',
                                           "**Language Instructions:**\n- You should respond in the same language as the user, supporting {supported_languages}.\n- Default language is {default_language}.")
        return template.format(supported_languages=', '.join(supported_langs), default_language=default_lang)

    def get_locale(self, lang_code: str = None) -> str:
        """Get locale for a given language code."""
        if not lang_code:
            return self.default_language
        _, locale = self.map_language_code(lang_code)
        return locale

    def get_system_prompt(self):
        """Build the complete system prompt."""
        language_instructions = self.get_language_instructions()
        return f'{self.profile_prompt} {self.config.mission.get_prompt(self.default_prompt)} {self.tools_prompt} {self.response_style} {language_instructions}'
