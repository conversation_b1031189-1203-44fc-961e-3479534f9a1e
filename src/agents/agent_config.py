import logging
from dataclasses import dataclass, field
from typing import Optional, List

import sentry_sdk
from typing_extensions import Literal

from config import get_config_value
from conv.conv_meta import VoiceConfig
from conv.room_config import RoomConfig

logger = logging.getLogger(__name__)
logger.propagate = False

def format_participant_context(participant_info: dict) -> str:
    """Formats raw participant context data into a single string with all key-value pairs."""
    if not participant_info:
        return "No participant context available"
    formatted_entries = []
    for key, value in participant_info.items():
        formatted_entries.append(f"{key}: {value}")
    return "Raw participant context - " + "; ".join(formatted_entries)


@dataclass
class Voice:
    voiceId: Optional[str] = None
    name: Optional[str] = None
    previewUrl: Optional[str] = None
    gender: Optional[str] = None
    accent: Optional[str] = None
    model: Optional[str] = None
    provider: Optional[str] = None
    voiceSettings: Optional[VoiceConfig] = None


@dataclass
class Mission:
    id: Optional[str] = None
    humanName: Optional[str] = None
    intro: Optional[str] = None
    goal: Optional[str] = None
    offerDetails: Optional[str] = None
    farewell: Optional[str] = None

    def get_prompt(self, default_prompt) -> str:
        # Get templates from configuration
        intro_suffix = get_config_value('messages', 'mission_intro_suffix',
                                        'Your name is {human_name}. You have to start call from clearly stating your role and goal.')
        prompt_separator = get_config_value('constants', 'mission.prompt_separator', '\n .')
        human_name_placeholder = get_config_value('constants', 'mission.human_name_placeholder', '{human_name}')

        self.intro += f" {intro_suffix.format(human_name=self.humanName)}"
        self.goal = self.goal or default_prompt
        parts = [self.intro, self.goal, self.offerDetails, self.farewell]
        return prompt_separator.join(filter(None, parts)).replace(human_name_placeholder, self.humanName)

FunctionType = Literal["send_follow_up_message", "schedule_callback", "donot_call"]

@dataclass
class AgentConfig:
    name: Optional[str] = None
    model: Optional[str] = None
    profile: Optional[str] = None
    voice: Optional[VoiceConfig] = None
    mission: Optional[Mission] = None
    functions: List[FunctionType] = field(default_factory=lambda: ["schedule_callback", "donot_call"])

def get_agent_info_from_meta(conv_meta: RoomConfig) -> AgentConfig:
    try:
        participant_info = conv_meta.context.participant
        participant_str = format_participant_context(participant_info) if participant_info else ""
        conv_meta.model_post_init()
        logger.info(f" conv meta func {conv_meta.functions}")

        agent_info = AgentConfig(
            name=conv_meta.llm.name,
            model=conv_meta.llm.model,
            profile=conv_meta.llm.profile if conv_meta.llm.profile else "empty",
            voice=conv_meta.voice,
            mission=Mission(
                intro=f"You have the following participant information: {participant_str} and you have to proactivelly reach your goal",
                humanName=conv_meta.llm.name,
                goal=conv_meta.llm.prompt
            ),
            functions=conv_meta.functions
        )
        logger.info("Successfully constructed AgentInfo from RoomConfig.")
        return agent_info
    except Exception as e:
        logger.exception(f"Error constructing AgentInfo from RoomConfig: {e}")
        sentry_sdk.capture_exception(e)
        raise

